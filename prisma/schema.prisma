// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false) @map("email_verified")
  image         String?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  // Relations
  sessions      Session[]
  accounts      Account[]

  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime @map("expires_at")
  token     String   @unique
  createdAt DateTime @map("created_at")
  updatedAt DateTime @map("updated_at")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  userId    String   @map("user_id")

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                     String    @id
  accountId              String    @map("account_id")
  providerId             String    @map("provider_id")
  userId                 String    @map("user_id")
  accessToken            String?   @map("access_token")
  refreshToken           String?   @map("refresh_token")
  idToken                String?   @map("id_token")
  accessTokenExpiresAt   DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt  DateTime? @map("refresh_token_expires_at")
  scope                  String?
  password               String?
  createdAt              DateTime  @map("created_at")
  updatedAt              DateTime  @map("updated_at")

  // Relations
  user                   User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime? @default(now()) @map("created_at")
  updatedAt  DateTime? @default(now()) @map("updated_at")

  @@map("verification")
}
