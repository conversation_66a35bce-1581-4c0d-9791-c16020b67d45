<svg width="166" height="135" viewBox="12 0 166 135" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="94.5" cy="67.5" r="59.5" fill="#F5F6F6"/>
<g filter="url(#filter0_d)">
  <rect x="22" y="26" width="145" height="72" rx="6" fill="white"/>
  <rect x="22.25" y="26.25" width="144.5" height="71.5" rx="5.75" stroke="#E9F7F0" stroke-width="0.5"/>
</g>
<g filter="url(#filter1_dd)">
  <rect x="12" y="37" width="166" height="50" rx="8" fill="white"/>
  <rect x="12.25" y="37.25" width="165.5" height="49.5" rx="7.75" stroke="#E9F7F0" stroke-width="0.5"/>
</g>

<!-- Light gray background box -->
<rect x="20" y="45" width="34" height="34" rx="6" fill="#F5F6F6"/>

<!-- Perfectly Centered Video Camera Icon -->
<rect x="28.5" y="56" width="13" height="10" rx="2" fill="#B0B3B2"/>
<path d="M41.5 58L45.5 56V66L41.5 64V58Z" fill="#B0B3B2"/>

<!-- Lines -->
<rect x="63" y="49" width="72" height="4" rx="2" fill="#17A34A"/>
<rect x="63" y="61" width="64" height="4" rx="2" fill="#D5F2E0"/>
<rect x="63" y="73" width="94" height="4" rx="2" fill="#E9F7F0"/>

<defs>
  <filter id="filter0_d" x="20.125" y="25.0625" width="148.75" height="75.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feGaussianBlur stdDeviation="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.1 0 0 0 0 0.15 0 0 0 0 0.14 0 0 0 0.04 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
  </filter>
  <filter id="filter1_dd" x="0" y="37" width="190" height="73" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect1_dropShadow"/>
    <feOffset dy="4"/>
    <feGaussianBlur stdDeviation="3"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.1 0 0 0 0 0.15 0 0 0 0 0.14 0 0 0 0.05 0"/>
    <feBlend mode="normal" in2="effect1_dropShadow" result="effect1_dropShadow"/>
    <feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow"/>
    <feOffset dy="12"/>
    <feGaussianBlur stdDeviation="8"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.1 0 0 0 0 0.15 0 0 0 0 0.14 0 0 0 0.07 0"/>
    <feBlend mode="normal" in2="effect2_dropShadow" result="effect2_dropShadow"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
  </filter>
</defs>
</svg>
