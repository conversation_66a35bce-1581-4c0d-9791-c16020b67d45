import { type ReactNode } from "react";
import { Icons } from "@/components/icons";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

interface AuthLayoutProps {
  children: ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="flex min-h-screen flex-col md:flex-row">
        {/* Left Panel - Branding */}
        <div className="relative hidden h-screen w-full flex-col justify-between overflow-hidden bg-gradient-to-br from-primary/10 to-primary/5 p-8 md:flex md:w-1/2 lg:p-12">
          {/* Decorative elements */}
          <div className="absolute -left-20 -top-20 h-64 w-64 rounded-full bg-primary/10 blur-3xl" />
          <div className="absolute -right-40 bottom-0 h-80 w-80 rounded-full bg-primary/5 blur-3xl" />

          <div className="relative z-10">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Image
                  src="/logo.svg"
                  alt="EchoPilot Logo"
                  className="h-10 w-10"
                  width={40}
                  height={40}
                />
                <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-xl font-bold text-transparent">
                  EchoPilot
                </span>
              </div>
            </div>

            <div className="mt-24 space-y-6">
              <h1 className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl lg:text-6xl">
                Welcome to EchoPilot
              </h1>
              <p className="max-w-lg text-lg text-muted-foreground">
                Your AI-powered meeting assistant that handles scheduling,
                notes, and follow-ups so you can focus on what matters most.
              </p>

              <div className="space-y-4 pt-4">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Trusted by 10,000+ professionals
                  </p>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Icons.star
                          key={i}
                          className="h-4 w-4 text-yellow-400 fill-yellow-400"
                        />
                      ))}
                    </div>
                    <Badge
                      variant="outline"
                      className="border-primary/20 bg-primary/5 text-foreground"
                    >
                      4.9/5
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="relative z-10 text-sm text-muted-foreground">
            {new Date().getFullYear()} EchoPilot. All rights reserved.
          </div>
        </div>

        {/* Right Panel - Auth Form */}
        <div className="flex min-h-screen w-full items-center justify-center p-4 sm:p-8 md:w-1/2">
          <div className="w-full max-w-md space-y-8">
            <Card className="relative overflow-hidden border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-8">
                <div className="absolute -right-20 -top-20 h-64 w-64 rounded-full bg-primary/10 blur-3xl" />
                <div className="relative z-10">
                  <div className="flex justify-center md:hidden">
                    <div className="mb-8 flex items-center space-x-2">
                      <Avatar className="h-10 w-10 bg-primary">
                        <Icons.mail className="h-5 w-5 text-primary-foreground" />
                        <AvatarFallback>MA</AvatarFallback>
                      </Avatar>
                      <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-xl font-bold text-transparent">
                        MeetAI
                      </span>
                    </div>
                  </div>
                  {children}
                </div>
              </CardContent>
            </Card>

            {/* Mobile footer */}
            <CardFooter className="mt-8 justify-center p-4 text-sm text-muted-foreground md:hidden">
              © {new Date().getFullYear()} MeetAI. All rights reserved.
            </CardFooter>
          </div>
        </div>
      </div>
    </div>
  );
}
