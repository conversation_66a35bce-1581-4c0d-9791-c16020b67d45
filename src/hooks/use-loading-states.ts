import { useState, useCallback } from 'react';

type LoadingStates = Record<string, boolean>;
type SetLoadingFn = (key: string, loading: boolean) => void;
type UseLoadingStatesResult = [LoadingStates, SetLoadingFn];

export function useLoadingStates(initialStates: LoadingStates = {}): UseLoadingStatesResult {
  const [loadingStates, setLoadingStates] = useState<LoadingStates>(initialStates);

  const setLoading = useCallback((key: string, loading: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: loading
    }));
  }, []);

  return [loadingStates, setLoading];
}
