import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "@/db";
import { sendEmail } from "./email";

// Base URL for email verification links
const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export const auth = betterAuth({
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  socialProviders: {
    github: {
      enabled: true,
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    },
    google: {
      enabled: true,
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
      scope: ["email", "profile"],
    },
  },
  database: prismaAdapter(prisma),
  emailVerification: {
    baseUrl,
    sendVerificationEmail: async ({ user, token }) => {
      const verificationUrl = `${baseUrl}/auth/verify-email?token=${token}`;

      await sendEmail({
        to: user.email,
        subject: "Verify your EchoPilot account",
        text: `Please verify your email by clicking the following link: ${verificationUrl}`,
        html: `
          <div>
            <h2>Welcome to EchoPilot!</h2>
            <p>Please verify your email by clicking the button below:</p>
            <a href="${verificationUrl}" style="
              display: inline-block;
              padding: 10px 20px;
              background-color: #0070f3;
              color: white;
              text-decoration: none;
              border-radius: 4px;
              margin: 10px 0;
            ">
              Verify Email
            </a>
            <p>Or copy and paste this link into your browser:</p>
            <p>${verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
          </div>
        `,
      });
    },
  },
});
