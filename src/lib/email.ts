import nodemailer, { Transporter, TransportOptions } from "nodemailer";

// Validate required environment variables
const requiredEnvVars = ["SMTP_HOST", "SMTP_PORT", "SMTP_USER", "SMTP_PASS"];
const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);

if (missingVars.length > 0) {
  throw new Error(
    `Missing required SMTP environment variables: ${missingVars.join(", ")}`
  );
}

// Create SMTP transport configuration
const smtpConfig = {
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || "587", 10),
  secure: process.env.SMTP_SECURE === "true",
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
} as const;

// Create a Nodemailer transporter with explicit settings
const transporter: Transporter = nodemailer.createTransport(
  smtpConfig as TransportOptions
);

type SendEmailParams = {
  to: string;
  subject: string;
  html: string;
  text: string;
};

/**
 * Sends an email using <PERSON>demailer
 * @param options Email options
 * @returns Promise with success status and optional error
 */
export const sendEmail = async ({
  to,
  subject,
  html,
  text,
}: SendEmailParams) => {
  try {
    const from = process.env.EMAIL_FROM || "EchoPilot <<EMAIL>>";

    await transporter.sendMail({
      from,
      to,
      subject,
      text,
      html,
    });

    return { success: true };
  } catch (error) {
    console.error("Error sending email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

// Test the email configuration on startup
(async () => {
  if (process.env.NODE_ENV !== "production") {
    try {
      await transporter.verify();
      console.log("✅ Email server is ready to send messages");
    } catch (error) {
      console.error("❌ Email server connection failed:", error);
    }
  }
})();
