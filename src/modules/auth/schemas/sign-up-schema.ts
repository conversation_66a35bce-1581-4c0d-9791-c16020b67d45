import { z } from "zod";

export const signUpSchema = z
  .object({
    firstName: z.string().min(1, { message: "First name is required" }),
    lastName: z.string().min(1, { message: "Last name is required" }),
    email: z.string().email({ message: "Please enter a valid email address" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" })
      .regex(/[A-Z]/, { message: "Password must contain at least one uppercase letter" })
      .regex(/[a-z]/, { message: "Password must contain at least one lowercase letter" })
      .regex(/[0-9]/, { message: "Password must contain at least one number" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })
  .refine((data) => data.firstName.trim().length > 0, {
    message: "First name cannot be empty",
    path: ["firstName"],
  })
  .refine((data) => data.lastName.trim().length > 0, {
    message: "Last name cannot be empty",
    path: ["lastName"],
  });

export type SignUpFormValues = z.infer<typeof signUpSchema>;
