"use client";

import { useLoadingStates } from "@/hooks/use-loading-states";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  signInSchema,
  type SignInFormValues,
} from "@/modules/auth/schemas/sign-in-schema";
import { toast } from "sonner";
import { authClient } from "@/db/auth-client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Icons } from "@/components/icons";
import Link from "next/link";
import { SocialProvider } from "@/types/auth";




export default function SignInView() {
  const router = useRouter();
  const [loadingStates, setLoading] = useLoadingStates({
    google: false,
    github: false,
    submit: false
  });
  
  const isLoading = Object.values(loadingStates).some(Boolean);

  const form = useForm<SignInFormValues>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: SignInFormValues) => {
    setLoading('submit', true);
    const toastId = toast.loading("Signing in...");

    try {
      const { error } = await authClient.signIn.email({
        email: data.email,
        password: data.password,
      });

      if (error) throw error;

      toast.success("Signed in successfully!", { id: toastId });
      router.push("/dashboard");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred";
      toast.error(errorMessage, { id: toastId });
    } finally {
      setLoading('submit', false);
    }
  };

  const signInWithProvider = async (provider: SocialProvider) => {
    setLoading(provider, true);
    const toastId = toast.loading(`Signing in with ${provider}...`);

    try {
      const { error } = await authClient.signIn.social({
        provider,
      });

      if (error) throw error;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to sign in";
      toast.error(errorMessage, { id: toastId });
    } finally {
      setLoading(provider, false);
    }
  };

  return (
    <div className="mx-auto w-full max-w-md space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Welcome back</h1>
        <p className="text-muted-foreground">
          Enter your email to sign in to your account
        </p>
      </div>

      <div className="grid gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => signInWithProvider("google")}
          className="w-full hover:bg-accent/90 transition-colors cursor-pointer"
          disabled={isLoading}
        >
          {loadingStates.google ? (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Icons.google className="mr-2 h-4 w-4" />
          )}
          <span>Google</span>
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => signInWithProvider("github")}
          className="w-full hover:bg-accent/90 transition-colors cursor-pointer"
          disabled={isLoading}
        >
          {loadingStates.github ? (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Icons.gitHub className="mr-2 h-4 w-4" />
          )}
          <span>GitHub</span>
        </Button>
      </div>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Icons.mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="<EMAIL>"
                      className="pl-10"
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      disabled={isLoading}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel>Password</FormLabel>
                  <Link
                    href="/forgot-password"
                    className="text-sm font-medium text-primary hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
                <FormControl>
                  <div className="relative">
                    <Icons.lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="••••••••"
                      className="pl-10"
                      type="password"
                      autoComplete="current-password"
                      disabled={isLoading}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button 
            type="submit" 
            className="w-full hover:bg-primary/90 transition-colors cursor-pointer" 
            disabled={isLoading}
          >
            {loadingStates.submit && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            <span>Sign In</span>
          </Button>
        </form>
      </Form>

      <div className="mt-4 text-center text-sm">
        Don&apos;t have an account?{" "}
        <Link
          href="/sign-up"
          className="font-medium text-primary hover:underline"
        >
          Sign up
        </Link>
      </div>
    </div>
  );
}
